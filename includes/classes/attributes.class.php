<?php

class tcs_product_attributes {
    public $products_id;
    public $languages_id;
    public $attributes = array();
    public $options_values = array();
    public $variations = array();
    public $has_attributes = false;
    public $has_variations = false;
    public $default_attributes = array();
    public $selected_attributes = array();
    public $selected_variation = array();
    public $input_attributes;
    public $isPreset = false;
    private $cacheKey;
    private $cacheTime;
    private $dirty = false;
    // is true when class is being constructed


    function __construct($products_id = null,$languages_id = 1,$input_attributes = null) {
        global $attribute_cache;
        $this->isPreset = true;
        $this->input_attributes = $this->init_input_attributes($products_id, $input_attributes);


        $this->cacheKey = 'product_attributes_' . $this->products_id;
        $this->cacheTime = 3600; // 1 hour

        if (!empty($attribute_cache[$this->products_id])){
            $this->init_cache();
        }
        //if (!isset($input_attributes)){
        $this->languages_id = $languages_id;
        $this->has_attributes = $this->init_has_attributes();
        $this->has_variations = $this->init_has_variations();
        // //print_rr($this,'attirbits');
    }


    private function init_input_attributes($products_id, $input_attributes){
        // sanatize the strings these regexs allows digits and curly braces only

        $products_id = preg_replace('/[^0-9{}]/', '', urldecode($products_id));
        $exploded_id =  explode('{', $products_id,2);
        $this->products_id = (int)$exploded_id[0];
        if (is_array($input_attributes)){
            if (is_array(($input_attributes[array_key_first($input_attributes)]))){
                foreach($input_attributes as $key => $value){
                    $input_attributes[$key] = array_key_first($value);
                }
            }
            return $input_attributes;
        } else if (is_string($input_attributes)){
            $input_attributes = preg_replace('/[^0-9{}]/', '', urldecode($input_attributes));
            return $this->parse_attribute_string($input_attributes);
        } else if (is_numeric(strpos($products_id,'{'))) {
            return $this->parse_attribute_string('{' . $exploded_id[1]);
        } else {
            $this->isPreset = false;
        }
        return false;
    }
    private function init_has_attributes(){
        $q = tep_db_query("SELECT * FROM `products_attributes` where `products_id` = " . (int)$this->products_id);
        return tep_db_num_rows($q) > 0 ? true : false;
    }

    private function init_has_variations(){
        $q = tep_db_query("SELECT * FROM `products_variations` where `products_id` = " . (int)$this->products_id);
        return tep_db_num_rows($q) > 0 ? true : false;
    }

    private function init_cache(){
        if (!empty($attribute_cache[$this->products_id])){
            $temp_attribute_cache = $attribute_cache[$this->products_id];
        } else {
            $redis = new Redis();
            $redis->connect('127.0.0.1', 6379);
            $redis->set($this->cacheKey, json_encode($this), $this->cacheTime);

            if ($redis->exists($this->cacheKey)) {
                // Read from cache
                $temp_attribute_cache = json_decode($redis->get($this->cacheKey), true);
            } else {
                return false;
            }
        }
        $this->attributes = 		 $temp_attribute_cache->attributes;
        $this->variations = 		 $temp_attribute_cache->variations;
        $this->options_values = 	 $temp_attribute_cache->options_values;
        $this->default_attributes =  $temp_attribute_cache->default_attributes;
        $this->selected_attributes = $this->get_current_selected_attributes();

        unset($redis);
        unset($temp_attribute_cache);
        return true;
    }


    private function set_cache(){
        $redis = new Redis();
        $redis->connect('127.0.0.1', 6379);
        $redis->set($this->cacheKey, json_encode($this), $this->cacheTime);
    }

    public static function get_attributes_from_id($products_attributes_id){
        $attribute_query = tep_db_query("select * from products_attributes where products_attributes_id = '" . (int)$products_attributes_id . "'");
        if (tep_db_num_rows($attribute_query) > 0){
            $attribute = tep_db_fetch_array($attribute_query);
            return $attribute;
        } else {
            return false;
        }
    }

    private function init_attributes(){
        if (!empty($this->attributes)) return $this->attributes;
        $this->dirty = true;
        $output = array();
        $options_values=array();
        $products_options_sql= "  
			select distinct 
				pa.attribute_default,
				popt.products_options_id,
				popt.products_options_name
			from
				products_attributes pa
				JOIN products_options popt on pa.options_id = popt.products_options_id								
			where 
				pa.products_id='" . (int)$this->products_id . "' and 
				popt.language_id = '" . (int)$this->languages_id . "' 
			order by 
				popt.products_options_name";
        $products_options_query = tep_db_query($products_options_sql);
        //print_rr($products_options_sql,"FIRST SQL");
        $count = 0;
        while ($products_options = tep_db_fetch_array($products_options_query)) {

            $attribute_default = $products_options["attribute_default"] == 1 ? true : false;
            $initalData = Array(
                "products_options_name" => $products_options['products_options_name'],
                "attribute_default" => $attribute_default,
                "values" => array()
            );
            $output[$products_options['products_options_id']] = $initalData;
            //print_rr($output,'inital data');
            $products_options_values_sql = "
				select 
					pov.products_options_values_id, 
					pov.products_options_values_name,
					pa.options_values_price, 
					pa.price_prefix, 
					pa.attribute_default,
					pa.products_attributes_id,
					pa.dependson_options_id, 
					pa.dependson_options_values_id,
					pa.products_attributes_sort_order
				from
					products_attributes pa, 
					products_options_values pov 
				where 
					pa.products_id = '" . (int)$this->products_id . "' 
					and pa.options_id = '" . (int)$products_options['products_options_id'] . "' 
					and pa.options_values_id = pov.products_options_values_id and pov.language_id = '" . (int)$this->languages_id . "' 
					order by pa.products_attributes_sort_order";


            //print_rr($products_options_values_sql, "SECOND SQL");
            $products_options_values_query = tep_db_query($products_options_values_sql);
            $i = 0;
            //print_rr($products_options_values_sql, "SECOND SQL");
            while ($products_options_values = tep_db_fetch_array($products_options_values_query)) {

                $value = array(
                    "enabled" => 0,
                    "products_attributes_id" => $products_options_values['products_attributes_id'],
                    "products_options_values_name" => $products_options_values['products_options_values_name'],
                    "options_values_price" =>  $products_options_values['options_values_price'],
                    "price_prefix" =>  $products_options_values['price_prefix'],
                    "attribute_default" =>  $products_options_values['attribute_default'],
                    "dependson_options_id" =>  $products_options_values['dependson_options_id'],
                    "dependson_options_values_id" =>  $products_options_values['dependson_options_values_id'],
                    "products_attributes_sort_order" => $products_options_values['products_attributes_sort_order']
                );
                //print_rr($value, "BUILT VALUE");
                $output[$products_options['products_options_id']]['values'][$products_options_values['products_options_values_id']] = $value;
                $options_values[$products_options_values['products_options_values_id']] = $value;
                if ($products_options_values['attribute_default'] == 1 || $i == 0){
                    $this->default_attributes[$products_options['products_options_id']] = $products_options_values['products_options_values_id'];
                }
                $i++;
            }
            //set default attrib
            $output[$products_options['products_options_id']]['values'][$this->default_attributes[$products_options['products_options_id']]]["attribute_default"] = 1;
        }
        $this->attributes = $output;
        $this->options_values = $options_values;
        $this->selected_attributes = $this->get_current_selected_attributes();
        $attribute_cache[$this->products_id] = $this;
        $this->dirty = false;
        $this->set_cache();
        //print_rr($this,"final object");
        return $this->attributes;
    }

    public function get_attributes($check_for_variations = true){
        if (empty($this->attributes)) $this->init_attributes();
        if (!$this->dirty && empty($this->variations)) $this->init_variations();
        foreach ($this->attributes as $keya=>$attributes){
            foreach ($attributes['values'] as $keyb=>$options){
                if ($check_for_variations){
                    if ($options['enabled'] == 0){
                        unset($this->attributes[$keya]['values'][$keyb]);
                    }
                }			}
            if (empty($this->attributes[$keya]['values'])){
                unset($this->attributes[$keya]);
            }
        }
        return $this->attributes;
    }

    private function init_variations(){
        if (empty($this->attributes)) $this->init_attributes();
        if (empty($this->attributes)) return false;
        $this->dirty = true;
        $output = array();
        $variations_sql = "select * 
			from products_variations pv
			join products p on p.products_id = pv.products_id 
		 WHERE pv.products_id = '" . $this->products_id . "'
		 order by sort_order";
        $variations_query = tep_db_query($variations_sql);
        if(tep_db_num_rows($variations_query)){
            while ($variations_values = tep_db_fetch_array($variations_query)) {
                $attributes = $this->parse_attribute_string($variations_values['attributes']);
                $product_name_suffix = $this->generate_product_suffix($attributes);
                //$product_url = tep_href_link('product_info.php', 'products_id=' . $this->products_id . $variations_values['attributes']);
                $product_image_url = "";
                if (@tep_not_null($variations_values['image_id'])){
                    $values = tep_db_query("select image from products_images where id = '" . $variations_values['image_id'] . "'");

                    if (tep_db_num_rows($values) > 0){
                        $values_values = tep_db_fetch_array($values);
                        $product_image_url = "https://www.cadservices.co.uk/images/" . $values_values["image"];
                    }
                }
                $autodesk_link = '';
                $autodesk_link_name = '';
                if ($variations_values['manufacturers_id'] == 15) {
                    if (empty($variations_values['autodesk_catalog_unique_hash'])){
                        $autodesk_link =  'NULL';
                        $autodesk_link_name =  'Autodesk product: Not linked';
                    } else {
                        $autodesk_link =  $variations_values['autodesk_catalog_unique_hash'];
                        $autodesk_link_name =  $this->get_autodesk_product_name($variations_values['autodesk_catalog_unique_hash']);
                    }
                }
                $output[] = array(
                    'products_variations_id' => $variations_values['products_variations_id'],
                    'autodesk_catalog_unique_hash' => $variations_values['autodesk_catalog_unique_hash'],
                    'products_id' => $variations_values['products_id'],
                    'attribute_string' => $variations_values['attributes'],
                    'product_name_suffix' => $product_name_suffix,
                    'manufacturers_id' => $variations_values['manufacturers_id'],
                    'model' => $variations_values['model'],
                    'price' => $variations_values['price'],
                    'gtin' => $variations_values['gtin'],
                    'image_url' => $product_image_url,
                    'image_id' => $variations_values['image_id'],
                    'attributes' => $attributes,
                    'autodesk_link' =>  $autodesk_link,
                    'autodesk_link_name' =>  $autodesk_link_name,
                    'enabled' => 1
                );
            }
        }
        $this->variations = $output;
        // enable attributes that have variations
        foreach ($output as $key_a=>$variation){
            foreach ($variation['attributes'] as $key_b=>$attrib){
                if (empty($this->attributes[$key_b]) || empty($this->attributes[$key_b]['values'][$attrib])){
                    $this->variations[$key_a]['enabled'] = 0;
                } else {
                    $this->attributes[$key_b]['values'][$attrib]['enabled'] = 1;
                }
            }
        }
        $this->set_cache();
        $this->dirty = false;
        return $this->variations;
    }

    private function get_autodesk_product_name($hash){
        if (empty($hash)) return false;
        $q = tep_db_query("SELECT id, orderAction, offeringName,accessModel_description, term_description, specialProgramDiscount_code 				
							FROM products_autodesk_catalog
							where unique_hash = '" . $hash ."'
							limit 1;");

        $qa = tep_db_fetch_array($q);
        $link_string = $qa['id'] . ': ' . $qa['orderAction'] . ' ' . $qa['offeringName'] . ' ' .  $qa['accessModel_description'] . ' ' . $qa['term_description'] . ' ' . $qa['specialProgramDiscount_code'];

        return tep_db_num_rows($q) > 0 ? $link_string : false;
    }

    public function get_variations(){
        if ($this->has_variations){
            $this->init_variations();
        }
        return $this->variations;
    }



    function get_current_selected_attributes(){
        //print_r($this);
        if 	($this->isPreset){
            $attribs = $this->get_attributes_from_array($this->input_attributes);
        } else {
            $attribs = $this->get_attributes_from_array($this->default_attributes);
        }
        return $attribs;
    }

    function get_current_selected_variation(){
        if 	($this->isPreset){
            $attribs = $this->input_attributes;
        }else{
            $attribs = $this->default_attributes;
        }
        //print_rr($attribs,'attribsvars');
        $variation = $this->get_variation_from_array($attribs);
        if ($variation) return $variation; else return false;
    }

    function get_price(){
        $attribs = $this->input_attributes;
        $variation = $this->get_variation_from_array($attribs);
        if (!empty($variation)) {
            $specials = tep_db_query("select * FROM specials where variations_id = " . $variation['products_variations_id'] . " AND status = 1 LIMIT 1");

            if (tep_db_num_rows($specials) > 0){
                $special = tep_db_fetch_array($specials);
                $thePrice = $special['specials_new_products_price'];

            } else {
                $thePrice = $variation['price'];
            }
        }
        print_rr(['q' => "select * FROM specials where variations_id = " . $variation['products_variations_id'] ,'numrows' => tep_db_num_rows($specials),'variation' => $variation,'special' => $special,'price' => $thePrice],'specials');
        //echo '<!--m:' . $thePrice  . '-->';
        if (isset($thePrice) && $thePrice > 0 )
            return $thePrice;
        else
            return false;
    }
    function get_model(){
        $attribs = $this->input_attributes;
        $variation = $this->get_variation_from_array($attribs);
        $the_model = $variation['model'];
        if (isset($the_model) && $the_model > 0 )
            return $the_model;
        else
            return false;
    }
    function get_image(){
        $attribs = $this->input_attributes;
        $variation = $this->get_variation_from_array($attribs);
        if (isset($variation['image']) && $variation['image'] > 0 )
            return $variation['image'];
        else
            return false;
    }
    function find_missing_variations() {
        if (empty($this->attributes)) $this->init_attributes();
        if (!$this->dirty && empty($this->variations)) $this->init_variations();

        $available_attributes = array_keys($this->attributes);
        $missing_variations = array();

        foreach ($available_attributes as $attribute) {
            $variations_with_attribute = array_filter($this->variations, function($variation) use ($attribute) {
                return isset($variation['attributes'][$attribute]);
            });

            if (count($variations_with_attribute) == count($available_attributes)) {
                $missing_variations[] = $variations_with_attribute;
            }
        }

        return $missing_variations;
    }


    function get_attributes_from_array($attribArray){
        $output = array();
        //print_rr( $this->attributes,'this->attributes ');
        //print_rr($attribArray,'this attribs: ');
        foreach($attribArray as $aKey => $attrib){
            if (is_array($attrib)) $attrib = array_key_first($attrib);
            $output[$aKey][$attrib] = $this->attributes[$aKey]['values'][$attrib];
        }

        return $output;
    }

    function get_variation_from_attrib($a){
        if(!is_array($a)){
            $array = $this->parse_attribute_string($a);
        } else {
            $array = $a;
        }
        return $this->get_variation_from_array($array);
    }


    function get_variation_from_array($attribs){
        if($this->has_variations){
            if (!$this->dirty && empty($this->variations)) $this->init_variations();
            foreach ($this->variations as $variation) {
                $dbAttrib = $this->parse_attribute_string($variation['attribute_string']);


                if (!$this->compare_attributes($attribs,$dbAttrib)){
                    //print_rr(['attribs' => $attribs,'dbAttrib' => $dbAttrib,'match' => 'false'],'get_variation_from_arraysss');
                    continue;
                } else {
                    //print_rr(['attribs' => $attribs,'dbAttrib' => $dbAttrib,'match' => 'true'],'get_variation_from_arraysss');
                    return $variation;
                }
            }
        }
        return false;
    }
    function get_variation_from_id($id){
        if($this->has_variations){
            if (!$this->dirty && empty($this->variations)) $this->init_variations();
            foreach ($this->variations as $variation){
                if ($variation['products_variations_id'] == $id) return $variation;
            }
        }
        return false;
    }    function get_attribute_from_id($id){
        if($this->has_attributes){
            if (!$this->dirty && empty($this->attributes)) $this->init_attributes();
            foreach ($this->attributes as $attribute){
                if ($attribute['products_attributes_id'] == $id) return $attribute;
            }
        }
        return false;
    }



    function parse_attribute_string($string){
        if (is_numeric(strpos($string, '{'))){
            $attributes = explode('{', substr($string, strpos($string, '{')+1));
            $attributes_array = array();
            for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {
                $pair = explode('}', $attributes[$i]);
                $attributes_array[(int)$pair[0]] = (int)$pair[1];
            }
            //print_rr($attributes_array);
            return $attributes_array;
        }else{
            return false;
        }
    }

    function generate_product_suffix($attributes = "", $separator = ', '){
        $product_name_suffix = "";
        if($this->has_variations){
            if (!$this->dirty && empty($this->variations)) $this->init_variations();
            if (!is_array($attributes)){
                $attributes = $this->parse_attribute_string($attributes);
                if(!$attributes) return false;
            }

            foreach($attributes as $attrib){
                $product_name_suffix .= $separator . $this->values_name($attrib);
            }
        }
        return $product_name_suffix;
    }

    function options_name($options_id) {
        if (isset($options_id)){
            return $this->attributes[$options_id]['products_options_name'];
        }
        return false;
    }

    function values_name($values_id) {
        if (isset($values_id)) return $this->options_values[$values_id]['products_options_values_name'];
        return false;
    }
    function compare_attributes($a, $b) {
        if (!is_array($a)) {
            $a_array = $this->parse_attribute_string($a);
        } else {
            $a_array = $a;
        }
        if (!is_array($b)) {
            $b_array = $this->parse_attribute_string($b);
        } else {
            $b_array = $b;
        }
        if (!is_array($a_array) || !is_array($b_array)) {
            return false;
        }

        // Normalize the arrays to have consistent data types (strings in this case)
        $a_array = array_map('strval', $a_array);
        $b_array = array_map('strval', $b_array);

        if (serialize($a_array) == serialize($b_array)) {
            return true;
        } else {
            return false;
        }
    }

    function attributes_customSort($a, $b) {
        // Ensure the arrays are valid and have at least two elements
        if (!is_array($a) || !is_array($b) || count($a) < 2 || count($b) < 2) {
            return 0; // Consider them equal if they are not proper arrays or don't have enough elements
        }
        $cmp = $a[0] - $b[0];
        // If the first values are equal, compare the second values
        if ($cmp === 0) {
            $cmp = $a[1] - $b[1];
        }

        return $cmp;
    }
}