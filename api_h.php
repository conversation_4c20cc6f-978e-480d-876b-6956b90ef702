<?php
require_once('includes/application_top.php');
require_once('includes/functions/tcs_components.php');
require_once('includes/functions/tcs_attributes_components.php');

$p = array_merge($_GET,$_POST);
switch($p['action']) {   
        case 'product_save':
            $response = product_variations_addToProduct(
				$p['variations_id'],
				$p['products_id'],
				$p['model'],
				$p['gtin'],
				$p['image_id'],
				$p['attribute'],
				$p['price'],
				$p['autodesk_link']
			);
            break;
        case 'product_variations_updateSortOrder':
            $response = product_variations_updateSortOrder($p['jsonData']);
            break;
        case 'product_variations_removeVariation':
            $response = product_variations_removeVariation($p['products_variations_id']);
            break;
        case 'product_autodesk_link_search':
           $response = product_autodesk_link_search($p['terms']);
            break;
		case 'product_variations_product_edit':
			$response = product_variations_product_edit($p['products_id'],$p['products_variations_id']);
			break;
		case "search_products":
			$response = search_products($p['search_keywords']);
			break;
		case "specials_update_variation_select":
			$response = specials_update_variation_select($p['special_products_select']);
			break;

		case "specials_update_products_price_field":
			$response = specials_update_products_price_field($p['variations_id']);
			break;
		case 'product_attributes_updateSortOrder':
			$products_attributes_id = isset($p['products_attributes_id']) ? (int)$p['products_attributes_id'] : 0;
			$sort_order = isset($p['sort_order']) ? (int)$p['sort_order'] : 0;
			$response =  product_attributes_updateSortOrder($products_attributes_id, $sort_order);
			break;
		case 'product_attributes_removeAttribute':
			$products_attributes_id = isset($p['products_attributes_id']) ? (int)$p['products_attributes_id'] : 0;
			$response =  product_attributes_removeAttribute($products_attributes_id);
			break;
		case 'product_attributes_getValueList':
			$options_id = isset($p['attributes_options_id']) ? (int)$p['attributes_options_id'] : 0;
 			$response =  product_attributes_getValueList($options_id);
			break;
		case 'product_attributes_getDependsOnValueList':
			$options_id = isset($p['attributes_dependson_options_id']) ? (int)$p['attributes_dependson_options_id'] : 0;
			$response =  product_attributes_getDependsOnValueList($options_id);
			break;
		case 'product_attributes_product_edit':
			$products_id = isset($p['products_id']) ? (int)$p['products_id'] : 0;
			$products_attributes_id = isset($p['products_attributes_id']) ? (int)$p['products_attributes_id'] : 0;
			$response =  product_attributes_product_edit($products_id, $products_attributes_id);
			break;
		case 'product_attributes_cancelEdit':
			$response =  product_attributes_cancelEdit();
			break;
		case 'product_attributes_updateAttribute':
			$products_id = isset($p['products_id']) ? (int)$p['products_id'] : 0;
			$products_attributes_id = isset($p['products_attributes_id']) ? (int)$p['products_attributes_id'] : 0;
			$products_attributes_id = isset($p['products_attributes_id']) ? (int)$p['products_attributes_id'] : 0;
			$edit_attributes_options_id = isset($p['edit_attributes_options_id']) ? $p['edit_attributes_options_id'] : '';
			$edit_attributes_values_id = isset($p['edit_attributes_values_id']) ? $p['edit_attributes_values_id'] : '';
			$edit_attributes_attribute_default = isset($p['edit_attributes_attribute_default']) ? $p['edit_attributes_attribute_default'] : '';
			$edit_attributes_dependson_options_id = isset($p['edit_attributes_dependson_options_id']) ? $p['edit_attributes_dependson_options_id'] : '';
			$edit_attributes_dependson_values_id = isset($p['edit_attributes_dependson_values_id']) ? $p['edit_attributes_dependson_values_id'] : '';
			$response =  product_attributes_updateAttribute($products_id, $products_attributes_id, $edit_attributes_options_id,
												   $edit_attributes_values_id, $edit_attributes_attribute_default,
												   $edit_attributes_dependson_options_id, $edit_attributes_dependson_values_id);
			break;
		case 'product_attributes_addToProduct':
           
			$response =  product_attributes_addToProduct(
                $p['products_id'],
                $p['attributes_options_id'],
                $p['attributes_values_id'],
                $p['attributes_attribute_default'],
                $p['attributes_dependson_options_id'],
                $p['attributes_dependson_values_id'],
                $p['product_attributes_addToProduct']);
			break;
}
echo $response;


function product_variations_addToProduct($variations_id = null,$products_id, $model, $gtin, $image_id, $attributes, $price,$autodesk_link = null) {
    
	$attribute_string = '';
	foreach ($attributes as $key => $value) {
		$attribute_string .= '{' . $key . '}' . $value;
	}
	$exists = tep_db_query("
	SELECT * 
	FROM `products_variations` 
	WHERE 
		`products_id` = {$products_id}
		AND `attributes` = '{$attribute_string}'
	");
	if (tep_db_num_rows($exists) > 0){
		$exists_results = tep_db_fetch_array($exists);	
		$variations_id = $exists_results['products_variations_id'];
	}	
	
	if ($autodesk_link != null){
		$qry_sql = "SELECT  `SRP`,`renewalDiscountAmount`,`transactionVolumeDiscountAmount`,`serviceDurationDiscountAmount`      
				FROM 
					`products_autodesk_catalog`
				WHERE 
					`unique_hash` = '{$autodesk_link}'				
				";
		$qry = tep_db_fetch_array(tep_db_query($qry_sql));
		$price =  $qry['SRP'];
		$price -= $qry['renewalDiscountAmount'] ?? 0;
		$price -= $qry['transactionVolumeDiscountAmount'] ?? 0;
		$price -= $qry['serviceDurationDiscountAmount'] ?? 0;
	}
	
	if ($products_id == '' || $products_id == null ) {
		return "Error: no product id supplied";
	}
	
	$variations_id = ($variations_id != '' && $variations_id != null) ? $variations_id : 'NULL';	
	$model = ($model != '' && $model != null) ? $model : 'NULL';
	$gtin = ($gtin != '' && $gtin != null) ? $gtin : 'NULL';
	$image_id = ($image_id != '' && $image_id != null) ? $image_id : 'NULL';
	$attribute_string = ($attribute_string != '' && $attribute_string != null) ? $attribute_string : 'NULL';
	$price = ($price != '' && $price != null) ? $price : 0;
	$autodesk_link = ($autodesk_link != '' && $autodesk_link != null) ? $autodesk_link : 'NULL';
	
	$query_sql = "INSERT INTO `products_variations` 
			(
				`products_variations_id`, 
				`products_id`, 
				`model`, 
				`gtin`, 
				`image_id`, 
				`attributes`, 
				`price`, 
				`autodesk_catalog_unique_hash`
			) VALUES ( 
				{$variations_id},
				{$products_id},  
				'{$model}', 
				'{$gtin}', 
				{$image_id}, 
				'{$attribute_string}',
				{$price},
				'{$autodesk_link}'
		) ON DUPLICATE KEY UPDATE   
			`products_id` = VALUES(`products_id`),
			`model` = VALUES(`model`),
			`gtin` = VALUES(`gtin`),
			`image_id` = VALUES(`image_id`),
			`attributes` = VALUES(`attributes`),
			`price` = VALUES(`price`),
			`autodesk_catalog_unique_hash` = VALUES(`autodesk_catalog_unique_hash`)
		"; 

	$rows = tep_db_affected_rows(tep_db_query(str_replace(["\n","\r","\t"], "", $query_sql)));
	
    if ($variations_id == 'NULL') {
		$variations_id  = tep_db_insert_id();
    } 
	//print_rr($variations_id,'insertid');
	
	$attributes_object = new tcs_product_attributes($products_id);
	$variation = $attributes_object->get_variation_from_id($variations_id);
	//print_rr($variation);
	$params = [];
	if (tep_db_num_rows($exists) > 0){
		$params['hx-swap-oob'] = 'true';
	}
    $output = tcs_draw_variations_table_row($variation,$params);
	
    //print_rr($variations_query);
	return $output;
}
function product_variations_product_edit($products_id,$products_variations_id){

	$attributes_object = new tcs_product_attributes($products_id);
	$variation = $attributes_object->get_variation_from_id($products_variations_id);
	/*
		'products_variations_id' => $variations_values['products_variations_id'],
		'attribute_string' => $variations_values['attributes'],
		'product_name_suffix' => $product_name_suffix,
		'model' => $variations_values['model'],
		'price' => $variations_values['price'],
		'gtin' => $variations_values['gtin'],
		'image_url' => $product_image_url,
		'image_id' => $variations_values['image_id'],
		'attributes'=> $attributes,
		'autodesk_link' =>  $variations_values['products_autodesk_catalog_id'],
		'enabled' => 1
	*/
	$values['model'] =			 $variation['model'];
	$values['gtin'] =			 $variation['gtin'];
	$values['image_id'] =		 $variation['image_id'];
	$values['price'] =			 $variation['price'];
	$values['products_id'] =	 $products_id;
	foreach ($variation['attributes'] as $key => $attribute){
		$values["attribute[{$key}]"] = $attribute;
	}
	$values['autodesk_link'] = $variation['autodesk_link'];
	$values['autodesk_link_value_name'] = $variation['autodesk_link_name'];

	return tcs_draw_variations_form(null, $products_id,$values);
};

function product_variations_removeVariation($products_variations_id) {	
	tep_db_query("DELETE FROM products_variations WHERE products_variations_id = " . $products_variations_id);
    $variations_query = tep_db_query("select * FROM products_variations WHERE products_variations_id = " . $products_variations_id);
	return "";
}

function product_autodesk_link_search($search_term) {
	if (!empty($search_term)) {
		// search products
		$query = "SELECT * FROM products_autodesk_catalog WHERE ";
		$words = explode(" ", $search_term);
		
		// Get table columns
		
		$columns = ['offeringName', 'intendedUsage_description', 'accessModel_description', 'servicePlan_description', 'connectivity_description', 'term_description'];
				
		// Build the query
		$conditions = [];
		foreach ($words as $word) {
			$word = trim($word);
			if (!empty($word)) {
				$word_conditions = [];
				foreach ($columns as $column) {
					$word_conditions[] = $column . " LIKE '%" . tep_db_input($word) . "%'";
				}
				if (!empty($word_conditions)) {
					$conditions[] = "(" . implode(" OR ", $word_conditions) . ")";
				}
			}
		}
		
		if (!empty($conditions)) {
			$query .= implode(" AND ", $conditions);
		} else {
			// No valid search terms, handle this case as needed
			//$query = "SELECT * FROM products_autodesk_catalog"; // or some other default behavior
		}
		$query .= " AND NOT (`intendedUsage_code` = 'NFR')";
		$query .= " ORDER BY orderAction, offeringName, accessModel_description, term_description, specialProgramDiscount_code";
		// Execute the query		
		try {
            $html ="";
			$results = tep_db_fetch_all(tep_db_query($query));			
			//$results[] = array("columns" => $columns);			
			//$results[] = array("query" => $query);
			
			foreach ($results as $key => $result) {
				$output =""; 
			if(!empty($result['orderAction'])) $output .= "{$result['orderAction']}"; 
			if(!empty($result['offeringName'])) $output .= ", {$result['offeringName']}";
			if(!empty($result['term_description'])) $output .= ", {$result['term_description']}";
			if(!empty($result['specialProgramDiscount_code'])) $output .= ", {$result['specialProgramDiscount_code']}";
			if(!empty($result['fromQty'])) $output .= ", From {$result['fromQty']} to {$result['toQty']}";
                $html .= "<option value='{$result['unique_hash']}'>{$output}</option>";
            }

			return $html;
		} catch (PDOException $e) {		
			return  json_encode(['error' => "Could not search data: " . $e->getMessage()]);
		}
	} else{
		return json_encode(['error' => "Could not search data: No search term provided"]);
	}
}


function product_autodesk_link_setLink($products_id,$unique_hash){ 
	
	//$query = "select * FROM products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac on p2a.unique_hash = pac.id  WHERE " . "products_id = {$products_id} AND unique_hash = {$unique_hash}";
	$query_sql = "select * from products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac on p2a.unique_hash = pac.unique_hash WHERE p2a.products_id = {(int) $products_id} Limit 1";
	//get product hash
	$query_hash_sql = "select * from products_autodesk_catalog WHERE unique_hash = {$unique_hash} LIMIT 1";
	$hash_query = tep_db_fetch_array(tep_db_query($query_hash_sql));	
	$hash = $hash_query['unique_hash'];
	if (tep_db_num_rows(tep_db_query($query_sql)) > 0) {
		$type = "update";	
		$query_set_sql = "UPDATE products_to_autodesk_catalog SET " . "unique_hash = {$unique_hash}} WHERE " . "products_id = {$products_id}";
	} else {	
		$type = "insert";			
		$query_set_sql  = "INSERT INTO products_to_autodesk_catalog (products_id, unique_hash) VALUES ({$products_id}, {$unique_hash},)";
	}
	tep_db_query($query_sql);
	$results = tep_db_fetch_array(tep_db_query($query_sql));
	return json_encode(array("Complete" => 1, "type" => $type, "product" => $results, "query" => $query_set_sql ));
}

function search_products($searchTerm) {
    // Split the search term into individual words
    $words = explode(' ', $searchTerm);
    
    $searchConditions = [];
    foreach ($words as $word) {
        $word = trim($word);
        if (!empty($word)) {
            // Prepare each word for the LIKE clause and safely escape it
            $likeClause = "'%" . tep_db_input($word) . "%'";
            $searchConditions[] = "(pd.products_name LIKE $likeClause OR p.products_model LIKE $likeClause OR m.manufacturers_name LIKE $likeClause)";
        }
    }

    // Combine all search conditions with AND
    $conditions = implode(' AND ', $searchConditions);
    
    // Construct the full SQL query using the conditions
    $products_query = tep_db_query("SELECT p.products_id, pd.products_name, cd.categories_name 
                                    FROM products p
                                    JOIN products_description pd ON pd.products_id = p.products_id
                                    JOIN manufacturers m ON m.manufacturers_id = p.manufacturers_id
                                    JOIN products_to_categories p2c ON p.products_id = p2c.products_id
                                    JOIN categories_description cd ON cd.categories_id = p2c.categories_id
                                    WHERE " . $conditions . " AND p.products_status = 1
                                    ORDER BY p.products_sort_order");

    $response = "";
    while ($products = tep_db_fetch_array($products_query)) {
        $productName = preg_replace(array('/ +/', '/[<>()""]/'), array(' ', ''), trim(strip_tags($products['products_name'])));
        $categories_name = preg_replace(array('/ +/', '/[<>()""]/'), array(' ', ''), trim(strip_tags($products['categories_name'])));
		$response .= "<option value='" . $products['products_id'] . "'>" . $productName . ' [' . $categories_name . ']</option>';
        // $response[] = array(
        //     "title" => $productName . ' [' . $categories_name . ']',
        //     "id" => $products['products_id']
        // );
    }
    return $response;

	
}

function specials_update_variation_select($products_id){
	$specials_array = [];
	// create an array of products on special, which will be excluded from the pull down menu of products
	// (when creating a new product on special)
	// $specials_array = array();
	// $specials_query = tep_db_query("select p.products_id from products p, specials s where s.products_id = p.products_id");
	// while ($specials = tep_db_fetch_array($specials_query)) {
	//   $specials_array[] = $specials['products_id'];
	// }


  $variations = $attribs->get_variations();
  $variation_ops = [];
  print_rr($variations);
  $options = '';
  foreach ($variations as $variation) {
	$options .= "<option value='{$variation['products_variations_id']}'>{$variation['model']} ({$variation['price']})</option>";
	// $variation_ops[$variation['products_variations_id']] = $variation['model'] . " ({$variation['price']})";
  }

  return $options;
} 

function specials_update_products_price_field($var_id){
  	$variation = tep_db_fetch_array( tep_db_query("SELECT * FROM `products_variations` where `products_variations_id` = " . $var_id) );
	return tep_draw_hidden_field('products_price', (isset($variation['price']) ? $variation['price'] : ''),'hx-include="true"');
}

// Add these new endpoints to api_h.php

function product_attributes_updateSortOrder($products_attributes_id, $sort_order) {
    $orders_sql = "UPDATE products_attributes SET products_attributes_sort_order = " . (int)$sort_order . 
                  " WHERE products_attributes.products_attributes_id = " . (int)$products_attributes_id;
    $orders_query = tep_db_query($orders_sql);
    return "Sort order updated successfully.";
}

function product_attributes_removeAttribute($products_attributes_id) {
    $delete_sql = "DELETE FROM products_attributes WHERE products_attributes_id = " . (int)$products_attributes_id;
    tep_db_query($delete_sql);
    return "";
}

function product_attributes_getValueList($options_id, $filter = null) {
    global $languages_id;
    $values_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                 FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                 WHERE povtpo.products_options_id = '" . (int)$options_id . "' 
                                 AND povtpo.products_options_values_id = pov.products_options_values_id 
                                 AND pov.language_id = '" . (int)$languages_id . "' 
                                 ORDER BY pov.products_options_values_name");
    print_rr($values_query);
    
    $options_html = '<option disabled selected value>Values</option>';
    while ($values = tep_db_fetch_array($values_query)) {
        $options_html .= '<option value="' . $values['products_options_values_id'] . '">' . 
                         $values['products_options_values_name'] . '</option>';
    }
    return $options_html;
}

function product_attributes_getDependsOnValueList($options_id) {
    global $languages_id;
    
    $values_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                 FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                 WHERE povtpo.products_options_id = '" . (int)$options_id . "' 
                                 AND povtpo.products_options_values_id = pov.products_options_values_id 
                                 AND pov.language_id = '" . (int)$languages_id . "' 
                                 ORDER BY pov.products_options_values_name");
    
    $options_html = '<option disabled selected value>Values</option>';
    while ($values = tep_db_fetch_array($values_query)) {
        $options_html .= '<option value="' . $values['products_options_values_id'] . '">' . 
                         $values['products_options_values_name'] . '</option>';
    }
    
    return $options_html;
}

function product_attributes_product_edit($products_id, $products_attributes_id) {
    $products_attributes = new tcs_product_attributes($products_id);
    $form_html = tcs_draw_attributes_form_footer($products_id, $products_attributes_id, $products_attributes);
    return $form_html;
    /*
    $attribute_query = tep_db_query("SELECT * FROM products_attributes WHERE products_attributes_id = " . 
                                    (int)$products_attributes_id);
    $attribute = tep_db_fetch_array($attribute_query);
    
    // Get options and values for the form
    $options_query = tep_db_query("SELECT * FROM products_options WHERE language_id = " . 
                                 (int)$languages_id . " ORDER BY products_options_name");
    
    $form_html = '<div class="panel-footer">
        <div class="row">
            <div id="edit_attributes_left_container" class="col-xs-6">
                <div id="edit_attributes_options_container" class="form-inline form-group">
                    <label>Edit Attribute: </label><br/>
                    <select id="edit_attributes_options_id" class="form-control" 
                        hx-get="api_h.php?action=product_attributes_getValueList" 
                        hx-target="#edit_attributes_values_id" 
                        hx-trigger="change"
                        hx-include="this"
                        hx-params="options_id:value">
                        <option disabled value="">Options</option>';
    
    while ($options_values = tep_db_fetch_array($options_query)) {
        $selected = ($options_values['products_options_id'] == $attribute['options_id']) ? 'selected' : '';
        $form_html .= '<option ' . $selected . ' value="' . $options_values['products_options_id'] . '">' . 
                      $options_values['products_options_name'] . '</option>';
    }
    
    $form_html .= '</select>
                    <div id="edit_attributes_values_id_container" class="form-group">
                        <select id="edit_attributes_values_id" class="form-control">
                            <option disabled value>Values</option>';
    
    $values_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                 FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                 WHERE povtpo.products_options_id = '" . (int)$attribute['options_id'] . "' 
                                 AND povtpo.products_options_values_id = pov.products_options_values_id 
                                 AND pov.language_id = '" . (int)$languages_id . "' 
                                 ORDER BY pov.products_options_values_name");
    
    while ($values = tep_db_fetch_array($values_query)) {
        $selected = ($values['products_options_values_id'] == $attribute['options_values_id']) ? 'selected' : '';
        $form_html .= '<option ' . $selected . ' value="' . $values['products_options_values_id'] . '">' . 
                      $values['products_options_values_name'] . '</option>';
    }
    
    $form_html .= '</select>
                    </div>
                </div>

                <div id="edit_attributes_misc_container" class="form-inline form-group">                    
                    <input type="hidden" name="edit_attributes_price_prefix" id="edit_attributes_price_prefix" value="+">
                    <div class="checkbox"><label><input type="checkbox" name="edit_attributes_attribute_default"
                                                        id="edit_attributes_attribute_default" ' . 
                                                        ($attribute['attribute_default'] ? 'checked' : '') . '>&nbsp;Default</label></div>
                </div>
            </div>

            <div id="edit_attributes_dependson_container" class="col-xs-5">
                <label>Depends On: </label>
                <select id="edit_attributes_dependson_options_id"
                        data-productsid="' . (int)$products_id . '" class="form-control" 
                        hx-get="api_h.php?action=product_attributes_getDependsOnValueList" 
                        hx-target="#edit_attributes_dependson_values_id" 
                        hx-trigger="change"
                        hx-include="this"
                        hx-params="options_id:value">
                    <option disabled value>Options</option>
                    <option ' . (empty($attribute['dependson_options_id']) ? 'selected' : '') . '>None</option>';
    
    $options_query = tep_db_query("SELECT * FROM products_options WHERE language_id = " . 
                                 (int)$languages_id . " ORDER BY products_options_name");
    
    while ($options_values = tep_db_fetch_array($options_query)) {
        $selected = ($options_values['products_options_id'] == $attribute['dependson_options_id']) ? 'selected' : '';
        $form_html .= '<option ' . $selected . ' value="' . $options_values['products_options_id'] . '">' . 
                      $options_values['products_options_name'] . '</option>';
    }
    
    $form_html .= '</select>
                <select multiple id="edit_attributes_dependson_values_id" class="form-control">';
    
    if (!empty($attribute['dependson_options_id'])) {
        $values_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                     FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                     WHERE povtpo.products_options_id = '" . (int)$attribute['dependson_options_id'] . "' 
                                     AND povtpo.products_options_values_id = pov.products_options_values_id 
                                     AND pov.language_id = '" . (int)$languages_id . "' 
                                     ORDER BY pov.products_options_values_name");
        
        $dependson_values = explode(',', $attribute['dependson_options_values_id']);
        
        while ($values = tep_db_fetch_array($values_query)) {
            $selected = in_array($values['products_options_values_id'], $dependson_values) ? 'selected' : '';
            $form_html .= '<option ' . $selected . ' value="' . $values['products_options_values_id'] . '">' . 
                          $values['products_options_values_name'] . '</option>';
        }
    } else {
        $form_html .= '<option disabled selected value>Values</option>';
    }
    
    $form_html .= '</select>
            </div>
            <div id="edit_attributes_submit" class="col-xs-1 form-group">
                <label>Save: </label>
                <button id="edit_attributes_update_btn" class="btn btn-primary pull-right" type="button"
                    hx-post="api_h.php?action=product_attributes_updateAttribute"
                    hx-include="#edit_attributes_options_id,#edit_attributes_values_id,#edit_attributes_attribute_default,#edit_attributes_dependson_options_id,#edit_attributes_dependson_values_id"
                    hx-vals=\'{"products_id":"' . $products_id . '", "products_attributes_id":"' . $products_attributes_id . '"}\'
                    hx-target="#attributesTable_body"
                    hx-swap="outerHTML"
                    hx-indicator="#indicatorLines">Update</button>
                <button id="edit_attributes_cancel_btn" class="btn btn-default pull-right" type="button"
                    hx-get="api_h.php?action=product_attributes_cancelEdit"
                    hx-target="#attributes_form"
                    hx-swap="innerHTML">Cancel</button>
            </div>
        </div>
    </div>';
    ?*/
}

function product_attributes_cancelEdit() {
    return "";
}

function product_attributes_updateAttribute($products_id, $products_attributes_id, $edit_attributes_options_id, 
                                           $edit_attributes_values_id, $edit_attributes_attribute_default,
                                           $edit_attributes_dependson_options_id, $edit_attributes_dependson_values_id) {
    global $attributes_class;
    
    // Convert checkbox value
    $is_default = isset($edit_attributes_attribute_default) ? 1 : 0;
    
    // Handle "None" option for depends on
    $dependson_options_id = ($edit_attributes_dependson_options_id === "None") ? "" : $edit_attributes_dependson_options_id;
    
    // Convert array to comma-separated string if multiple values selected
    $dependson_values_id = "";
    if (is_array($edit_attributes_dependson_values_id)) {
        $dependson_values_id = implode(',', $edit_attributes_dependson_values_id);
    } else if (!empty($edit_attributes_dependson_values_id)) {
        $dependson_values_id = $edit_attributes_dependson_values_id;
    }
    
    // Update the attribute
    $sql = "UPDATE products_attributes SET 
            options_id = " . (int)$edit_attributes_options_id . ",
            options_values_id = " . (int)$edit_attributes_values_id . ",
            attribute_default = " . $is_default . ",
            dependson_options_id = " . (empty($dependson_options_id) ? "NULL" : (int)$dependson_options_id) . ",
            dependson_options_values_id = " . (empty($dependson_values_id) ? "NULL" : "'" . tep_db_input($dependson_values_id) . "'") . "
            WHERE products_attributes_id = " . (int)$products_attributes_id;
    
    tep_db_query($sql);
    
    // Regenerate the entire attributes table
    $attributes = new attributes((int)$products_id);
    $attributes_table = tcs_draw_attributes_table($attributes, (int)$products_id);
    
    // Extract just the table body from the full table HTML
    preg_match('/<tbody id="attributesTable_body">(.*?)<\/tbody>/s', $attributes_table, $matches);
    
    return '<tbody id="attributesTable_body">' . $matches[1] . '</tbody>';
}
function product_attributes_addToProduct($products_id, $attributes_options_id,$attributes_values_id, $attributes_attribute_default, $attributes_dependson_options_id, $attributes_dependson_values_id, $product_attributes_addToProduct,$attributes_id = null) {
    if (!$products_id) {
        return "Error: no product id supplied";
    }
    $attributes_object = new tcs_product_attributes($products_id);
    $attribute = $attributes_object->get_attribute_from_id($attributes_id);
    $params = [];
    if ($attribute) {
            $attributes_id = $attribute['products_attributes_id'];
            $params['hx-swap-oob'] = 'true';
        }
    }

    $attributes_id = ($attributes_id != '' && $attributes_id != null) ? $attributes_id : 'NULL';
    $attributes_options_id = ($attributes_options_id != '' && $attributes_options_id != null) ? $attributes_options_id : 'NULL';
    $attributes_values_id = ($attributes_values_id != '' && $attributes_values_id != null) ? $attributes_values_id : 'NULL';
    $attributes_attribute_default = ($attributes_attribute_default != '' && $attributes_attribute_default != null) ? $attributes_attribute_default : 0;
    $attributes_dependson_options_id = ($attributes_dependson_options_id != '' && $attributes_dependson_options_id != null) ? $attributes_dependson_options_id : 'NULL';
    $attributes_dependson_values_id = ($attributes_dependson_values_id != '' && $attributes_dependson_values_id != null) ? $attributes_dependson_values_id : 0;
    
    $query_sql = "INSERT INTO `products_attributes` (
			`products_attributes_id`, 
			`products_id`, 
			`options_id`, 
			`options_values_id`, 
			`attribute_default`,
			`dependson_options_id`, 
			`dependson_options_values_id`
	) VALUES ( 
			{$attributes_id},
			{$products_id},  
			'{$attributes_options_id}', 
			'{$attributes_values_id}', 
			{$attributes_attribute_default}, 
			'{$attributes_dependson_options_id}',
			{$attributes_dependson_values_id}
	) ON DUPLICATE KEY UPDATE   
			`products_id` = VALUES(`products_id`),
			`options_id` = VALUES(`options_id`),
			`options_values_id` = VALUES(`options_values_id`),
			`attribute_default` = VALUES(`attribute_default`),
			`dependson_options_id` = VALUES(`dependson_options_id`),
			`dependson_options_values_id` = VALUES(`dependson_options_values_id`)
	";

    $rows = tep_db_affected_rows(tep_db_query(str_replace(["\n","\r","\t"], "", $query_sql)));

    if (!is_numeric($attributes_id) ) $attributes_id  = tep_db_insert_id();


    
    return tcs_draw_attributes_table_row($attribute,$,$params);
}